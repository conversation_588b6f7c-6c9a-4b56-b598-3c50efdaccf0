import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

/// Utility class for structured logging with unique correlation ID generation.
/// 
/// This class provides a centralized logging interface for the parsing pipeline
/// that generates unique correlation IDs for each parse operation. All log messages
/// are prefixed with the correlation ID to enable easy filtering and tracking
/// of related log entries across the entire parsing flow.
/// 
/// Usage:
/// ```dart
/// // Start a new parse operation and get correlation ID
/// final parseId = ParseLogger.start("buy coffee 5$");
///
/// // Log messages with correlation ID
/// ParseLogger.i(parseId, "Trying strategy: MlKitStrategy");
/// ParseLogger.d(parseId, "ML Kit found 2 entities");
/// ParseLogger.w(parseId, "Ambiguous amount detected", error);
/// ParseLogger.e(parseId, "Strategy failed", error, stackTrace);
///
```
///
/// All log messages are formatted as: `[parse:<id>] <message>`
/// This allows easy filtering in logs: `grep "parse:a1b2c3d4" logs.txt`
class ParseLogger {
  static Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: false,
    ),
  );

  static const Uuid _uuid = Uuid();

  /// Starts a new parse operation and generates a unique correlation ID.
  /// 
  /// This method should be called at the beginning of each parse operation
  /// to generate a unique 8-character correlation ID and log the start message.
  /// 
  /// [text] The input text being parsed
  /// 
  /// Returns the generated correlation ID that should be used for all
  /// subsequent log messages related to this parse operation.
  static String start(String text) {
    final id = _uuid.v4().substring(0, 8);
    _logger.i('[parse:$id] START: "$text"');
    return id;
  }

  /// Logs a debug message with the specified correlation ID.
  /// 
  /// Debug messages are used for detailed tracing of the parsing flow
  /// and are typically only visible in debug builds or when debug
  /// logging is explicitly enabled.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The debug message to log
  static void d(String id, String message) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.d('[parse:$id] $message');
  }

  /// Logs an info message with the specified correlation ID.
  /// 
  /// Info messages are used for important events in the parsing flow
  /// such as strategy selection, successful parsing, etc.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The info message to log
  static void i(String id, String message) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.i('[parse:$id] $message');
  }

  /// Logs a warning message with the specified correlation ID.
  /// 
  /// Warning messages are used for recoverable errors or unexpected
  /// conditions that don't prevent parsing from continuing.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The warning message to log
  /// [error] Optional error object to include in the log
  static void w(String id, String message, [Object? error]) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.w('[parse:$id] $message', error: error);
  }

  /// Logs an error message with the specified correlation ID.
  /// 
  /// Error messages are used for serious errors that may prevent
  /// parsing from completing successfully.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The error message to log
  /// [error] Optional error object to include in the log
  /// [stackTrace] Optional stack trace to include in the log
  static void e(String id, String message, [Object? error, StackTrace? stackTrace]) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.e('[parse:$id] $message', error: error, stackTrace: stackTrace);
  }

  /// Sets a custom log output for testing purposes.
  ///
  /// This method allows injection of custom LogOutput implementations
  /// for testing, such as MemoryLogOutput to capture log records
  /// for verification in tests.
  ///
  /// [output] The LogOutput implementation to use
  static void setLogOutput(LogOutput output) {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 3,
        lineLength: 120,
        colors: true,
        printEmojis: false,
      ),
      output: output,
    );
  }
}
           this == ParseStatus.needsCategory ||
           this == ParseStatus.needsAmountConfirmation ||
           this == ParseStatus.missingAmount ||
           this == ParseStatus.ambiguousAmount;
  }

  /// Maps this ParseStatus to the corresponding PendingStage
  /// Returns null for statuses that don't require pending state
  PendingStage? toPendingStage() {
    switch (this) {
      case ParseStatus.needsType:
        return PendingStage.typeSelection;
      case ParseStatus.needsCategory:
        return PendingStage.categorySelection;
      case ParseStatus.needsAmountConfirmation:
      case ParseStatus.ambiguousAmount:
        return PendingStage.amountConfirmation;
      case ParseStatus.missingAmount:
        return PendingStage.missingAmount;
      default:
        return null;
    }
  }

  /// Returns true if this status requires amount confirmation
  bool get requiresAmountConfirmation {
    return this == ParseStatus.needsAmountConfirmation ||
           this == ParseStatus.ambiguousAmount;
  }
}
```

Add import for PendingStage: `import 'pending_transaction_state.dart';`

This extension provides helper methods for identifying soft-fail statuses and mapping them to appropriate pending stages, supporting the state management workflow in ChatScreen.

### test/models/pending_transaction_state_test.dart(NEW)

References: 

- test/models/parse_result_test.dart
- test/models/transaction_model_test.dart
- test/helpers/test_helpers.dart

Create comprehensive unit tests for PendingTransactionState class following the established testing patterns from the codebase.

Implement the following test structure:

**Imports and Setup:**
- Import `flutter_test`, the new model classes, and `test_helpers.dart`
- Use `setUp()` method to initialize common test data using `TestHelpers.createTestTransaction()` and `TestHelpers.createNeedsCategoryParseResult()` etc.

**Test Groups:**

1. **Factory Constructor Validation Group:**
   - Test `forTypeSelection()` succeeds with `ParseStatus.needsType` and throws `ArgumentError` with other statuses
   - Test `forCategorySelection()` succeeds with `ParseStatus.needsCategory` and throws `ArgumentError` with other statuses
   - Test `forAmountConfirmation()` succeeds with `ParseStatus.needsAmountConfirmation` and `ParseStatus.ambiguousAmount`, throws `ArgumentError` with other statuses
   - Test `forMissingAmount()` succeeds with `ParseStatus.missingAmount` and throws `ArgumentError` with other statuses
   - Test that all factory constructors properly set `parseResult`, `originalText`, and `stage` fields

2. **Getter Behavior Group:**
   - Test `isTypeSelection`, `isCategorySelection`, `isAmountConfirmation`, `isMissingAmount` return correct boolean values for each stage
   - Test `transaction` getter returns `parseResult.transaction`
   - Test `candidateAmounts` and `candidateTexts` getters return `parseResult.candidateAmounts` and `parseResult.candidateTexts`
   - Test `ambiguityType` getter returns `parseResult.ambiguityType`

3. **Equality and HashCode Group:**
   - Test that two instances with identical fields are equal and have same hashCode
   - Test that instances with different `parseResult`, `originalText`, or `stage` are not equal
   - Test hashCode consistency across multiple calls
   - Test equality with various combinations of field differences

4. **CopyWith Method Group:**
   - Test `copyWith()` with single field changes preserves other fields
   - Test `copyWith()` with multiple field changes
   - Test `copyWith()` with no parameters returns equal instance
   - Test `copyWith()` maintains validation constraints (if validation is added to copyWith)

5. **Immutability Group:**
   - Verify that all fields are final and cannot be modified after construction
   - Test that `copyWith()` returns a new instance rather than modifying the original

6. **ToString Method Group:**
   - Test that `toString()` includes all relevant fields
   - Test `toString()` with different stages and data combinations

7. **Edge Cases Group:**
   - Test with empty `originalText`
   - Test with null optional fields in `ParseResult` (candidateAmounts, candidateTexts, ambiguityType)
   - Test with various transaction types and currencies
   - Test with long `originalText` and complex `ParseResult` data

8. **Integration with ParseResult Group:**
   - Test that all data from `ParseResult` is accessible through convenience getters
   - Test with `ParseResult` instances created by `TestHelpers`
   - Test with different `ParseResult` factory constructors (success, needsCategory, etc.)

Use the established testing patterns:
- Follow the same `group()` and `test()` organization as existing model tests
- Use `TestHelpers` methods for creating test data
- Use `expect()` assertions with appropriate matchers
- Test error cases with `expectThrows<ArgumentError>()`
- Include descriptive test names and clear assertions

Create helper methods similar to `ParseResultMatchers` if needed for complex state validation.

### test/models/parse_status_extension_test.dart(NEW)

References: 

- test/models/parse_result_test.dart
- lib/models/parse_result.dart(MODIFY)

Create unit tests for the ParseStatus extension methods following established testing patterns.

Implement the following test structure:

**Imports:**
- Import `flutter_test` and the model classes with the new extension

**Test Groups:**

1. **isSoftFail Getter Group:**
   - Test that `isSoftFail` returns `true` for: `needsType`, `needsCategory`, `needsAmountConfirmation`, `missingAmount`, `ambiguousAmount`
   - Test that `isSoftFail` returns `false` for: `success`, `failed`
   - Create a comprehensive truth table test covering all ParseStatus enum values

2. **toPendingStage Method Group:**
   - Test that `needsType.toPendingStage()` returns `PendingStage.typeSelection`
   - Test that `needsCategory.toPendingStage()` returns `PendingStage.categorySelection`
   - Test that `needsAmountConfirmation.toPendingStage()` returns `PendingStage.amountConfirmation`
   - Test that `ambiguousAmount.toPendingStage()` returns `PendingStage.amountConfirmation`
   - Test that `missingAmount.toPendingStage()` returns `PendingStage.missingAmount`
   - Test that `success.toPendingStage()` returns `null`
   - Test that `failed.toPendingStage()` returns `null`

3. **requiresAmountConfirmation Getter Group:**
   - Test that `requiresAmountConfirmation` returns `true` for: `needsAmountConfirmation`, `ambiguousAmount`
   - Test that `requiresAmountConfirmation` returns `false` for all other ParseStatus values

4. **Consistency with ParseResult Group:**
   - Test that `isSoftFail` matches the existing `requiresUserInput` getter behavior in ParseResult
   - Test that the extension methods work correctly with ParseResult instances created by TestHelpers
   - Verify that the mapping logic aligns with the factory constructor validation in PendingTransactionState

5. **Enum Completeness Group:**
   - Test that all ParseStatus enum values are handled by the extension methods
   - Verify that adding new ParseStatus values would be caught by the tests (defensive testing)

Use the same testing patterns as other enum and extension tests in the codebase:
- Use descriptive test names
- Group related tests together
- Test all enum values explicitly
- Use `expect()` assertions with clear expected values
- Include edge case testing where applicable